import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, Subject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ManagedAccount } from './managed-account.model';

@Injectable({
  providedIn: 'root'
})
export class ManagedAccountService {
  private myAppUrl: string;
  private goToStepSubject = new Subject<number>();
  goToStep$ = this.goToStepSubject.asObservable();

  constructor(private http: HttpClient) {
    this.myAppUrl = environment.apiBaseUrl;
  }

  emitGoToStep(step: number): void {
    this.goToStepSubject.next(step);
  }

  saveManagedAccount(managedAccount: ManagedAccount): Observable<any> {
    return this.http
      .post<any>(`${this.myAppUrl}api/managed-accounts/add`, managedAccount)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getManagedAccountById(id: number): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts/${id}`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }

  getAllManagedAccounts(): Observable<any> {
    return this.http
      .get<any>(`${this.myAppUrl}api/managed-accounts/all`)
      .pipe(
        map((response: any) => response),
        catchError(this.errorHandler)
      );
  }
    
  errorHandler(error: any) {
    return throwError(error);
  }
}
