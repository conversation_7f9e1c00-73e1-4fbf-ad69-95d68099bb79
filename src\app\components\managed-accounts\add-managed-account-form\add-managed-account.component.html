<form [formGroup]="managedAccountForm" class="mr-4 ml-4" [ngClass]="{'form-container': step !== 3, 'review-page-form-container': step === 3, 'mb-4': step == 3}">
  <div class="row mr-0 ml-0 main-Container-height">
    <div class="col-md-2 step-sidebar d-flex pr-0">
      <div class="stepper-container">
        <div class="stepper" (click)="goToStep(1)" (keydown)="goToStep(1)">
            <div class="imageIcon-container ">
                <div class="stepper-line"></div>
                <img *ngIf="!isAccountFactsSaved" src="assets/dist/images/Groupdoc.svg" alt="Step 1" class="step-image mr-2">
                <img *ngIf="isAccountFactsSaved" src="assets/dist/images/greencheck.svg" alt="Saved" class="step-image mr-2">
                <div class="stepper-line"></div>
            </div>
            <div class="text-content">
              <div class="align-text step-title">
                Steps 1/2
              </div>
              <div class="step-subtitle custom-subtitlealign" [ngClass]="{ 'active-step': step === 1 }">
                <span>Managed Account Facts</span>
              </div>
            </div>
        </div>
        <div class="stepper" (click)="goToStep(2)" (keydown)="goToStep(2)">
          <div class="imageIcon-container ">
              <div class="stepper-line"></div>
              <img *ngIf="!isSummarySaved && !isSummaryEdited" src="assets/dist/images/disbluedoc.svg" alt="Step 2" class="step-image mr-2">
              <img *ngIf="!isSummarySaved && isSummaryEdited" src="assets/dist/images/Groupdoc.svg" alt="Step 2" class="step-image mr-2">
              <img *ngIf="isSummarySaved" src="assets/dist/images/greencheck.svg" alt="Saved" class="step-image mr-2">
              <div class="stepper-line"></div>
          </div>
          <div class="text-content">
            <div class="align-text step-title">
              Steps 2/2
            </div>
            <div class="step-subtitle custom-subtitlealign" [ngClass]="{ 'active-step': step === 2 }">
              <span>Investment Summary</span>
            </div>
          </div>
        </div>
      </div>
    </div>
   
    <div class="col-md-10 pr-4 pl-4" [ngClass]="{'custom-Fixedheight': step !== 3, 'review-page-custom-Fixedheight': step === 3, 'pb-4': step !== 3}">
      <div *ngIf="step === 1">
        <div class="row mr-0 ml-0">
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('managedAccountName').invalid && submitted}">
            Managed Account Name
          </div>
          <kendo-textbox formControlName="managedAccountName" placeholder="Type here" id="managedAccountName"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('domicile').invalid && submitted}">
            Domicile
          </div>
          <kendo-textbox formControlName="domicile" placeholder="Type here" id="domicile"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('commencementDate').invalid && submitted}">
            Commencement date
          </div>          
          <kendo-datepicker formControlName="commencementDate" placeholder="Type here" (focus)="setCurrentDate('commencementDate')" id="commencementDate"
            format="d MMMM yyyy" class="custom-input"></kendo-datepicker>
        </div>

        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('investmentPeriodEndDate').invalid && submitted}">
            Investment period end date
          </div>
          <kendo-textbox formControlName="investmentPeriodEndDate" placeholder="Type here" id="investmentPeriodEndDate"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('maturityDate').invalid && submitted}">
            Maturity date
          </div>
           <kendo-textbox formControlName="maturityDate" placeholder="Type here" id="maturityDate"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('commitmentOutstanding').invalid && submitted}">
            Commitment Outstanding
          </div>
          <kendo-textbox formControlName="commitmentOutstanding" placeholder="Type here"  type="number" id="commitmentOutstanding"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('baseCurrency').invalid && submitted}">
            Base Currency
          </div>
          <kendo-textbox formControlName="baseCurrency" placeholder="Type here" id="baseCurrency"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('investmentManager').invalid && submitted}">
            Investment Manager
          </div>
          <kendo-textbox formControlName="investmentManager" placeholder="Type here" id="investmentManager"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('administrator').invalid && submitted}">
            Administrator
          </div>
          <kendo-textbox formControlName="administrator" placeholder="Type here" id="administrator"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('custodian').invalid && submitted}">
            Custodian
          </div>
          <kendo-textbox formControlName="custodian" placeholder="Type here" id="custodian"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('legalCounsel').invalid && submitted}">
            Legal Counsel
          </div>
          <kendo-textbox formControlName="legalCounsel" placeholder="Type here" id="legalCounsel"
            class="custom-input"></kendo-textbox>
        </div>
        
        <div class="col-md-4 mb-4">
          <div class="custom-marginbottom" [ngClass]="{'label-error': managedAccountForm.get('lei').invalid && submitted}">
            LEI
          </div>
          <kendo-textbox formControlName="lei" placeholder="Type here" id="lei"
            class="custom-input"></kendo-textbox>
        </div>
        </div>
      </div>

      <div *ngIf="step === 2">
        <label for="investmentSummary">Summary</label>
        <textarea id="investmentSummary" formControlName="SummaryCount" class="form-control" rows="20"
          (input)="updateCharCount()" maxlength="6000"></textarea>
        <div class="char-count">{{ charCount }}/6000</div>
      </div>

      <div *ngIf="step === 3">
        <managed-account-review-form [ManagedAccountModel]="savedData">
        </managed-account-review-form>
      </div>

      <div class="row">
        <div class="pl-4" [ngClass]="{'pb-3': step !== 3, 'pr-4': step !== 3, 'footer': step !== 3, 'review-page-custom-footer': step == 3, 'pr-2': step == 3}">
          <div class="btn-reset-container">
            <button id="btn-reset" (click)="onReset()" class="btn TextTruncate btn-warning mr-2 TextTruncate"
              [ngClass]="{'disabled-btn': !isEdited, 'd-none': step === 3}" [disabled]="!isEdited">Reset</button>
          </div>
          <div>
            <button id="btn-cancel" (click)="onCancel()"
              class="btn TextTruncate btn-warning mr-2 TextTruncate">Cancel</button>
            <button id="btn-save" class="btn-save" (click)="onSubmit()"
              [ngClass]="{'disabled-save-btn': !isEdited, 'd-none': step === 3}" [disabled]="!isEdited">Save</button>
            <button id="btn-add-account" class="btn-save" (click)="saveManagedAccount()"
              [ngClass]="{'d-none': step !== 3}">Add Account</button>
          </div>
        </div>
      </div>

      <div *ngIf="showPopup">
        <confirm-modal IsInfoPopup="true" customwidth="600px" modalTitle="Added a Managed Account" primaryButtonName="OK" (primaryButtonEvent)="onCloseDialog()">
          <div>
            <div class="oprtnkpi-lh d-flex align-items-center justify-content-start">
              <div>
                <img src="assets/dist/images/greencheck.svg" alt="Saved Image" class="mr-2">
              </div>
              <div class="popup-text">
                You have successfully added <b>{{savedData.managedAccountName}}</b> 
              </div>
            </div>
          </div>
        </confirm-modal>
      </div>
    </div>
  </div>
</form>

<app-loader-component *ngIf="isLoading"></app-loader-component>
