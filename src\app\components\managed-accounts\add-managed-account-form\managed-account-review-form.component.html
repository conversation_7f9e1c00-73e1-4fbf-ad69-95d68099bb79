<div class="main-container pr-2" id="managed-account-review-form">

    <div class="account-facts-container">
      
      <div class="d-flex align-items-center mt-3 mb-2" id="account-facts">
        <div class="header-field">
          Managed Account Facts            
        </div>
        <div class="edit-Button" (click)="triggerGoToStep(1)">
          <img src="assets/dist/images/k-edit.svg" alt="Edit" class="ml-4">
        </div>
      </div>

      <div class="row mr-0 ml-0 card-body">
        <div class="col-md-4 padding-card-rem" id="managed-account-name">                      
          <label class="mb-0" for="managedAccountName">Managed Account Name</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100"
            [value]="ManagedAccountModel.managedAccountName"/>         
        </div>   
        <div class="col-md-4 padding-card-rem" id="domicile">                      
          <label class="mb-0" for="domicile">Domicile</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100"
            [value]="ManagedAccountModel.domicile"/>         
          </div>
        <div class="col-md-4 padding-card-rem" id="commencement-date">                      
          <label class="mb-0" for="commencementDate">Commencement Date</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100" 
            [value]="getformattedDateString(formattedManagedAccountModel.commencementDate,'d MMMM yyyy')"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="investment-period-end-date">                      
          <label class="mb-0" for="investmentPeriodEndDate">Investment Period End Date</label>
          <input type="text" 
            class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
            name="input-text" 
            autocomplete="off" 
            maxlength="100" 
            [value]="ManagedAccountModel.investmentPeriodEndDate"/>         
        </div>   
        <div class="col-md-4 padding-card-rem" id="maturity-date">                      
          <label class="mb-0" for="maturityDate">Maturity Date</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100"
          [value]="formattedManagedAccountModel.maturityDate" />         
        </div>   
        <div class="col-md-4 padding-card-rem" id="commitment-outstanding">                      
          <label class="mb-0" for="commitmentOutstanding">Commitment Outstanding</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.commitmentOutstanding"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="base-currency">                      
          <label class="mb-0" for="baseCurrency">Base Currency</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.baseCurrency"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="investment-manager">                      
          <label class="mb-0" for="investmentManager">Investment Manager</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.investmentManager"/>         
        </div>
        <div class="col-md-4 padding-card-rem" id="administrator">                      
          <label class="mb-0" for="administrator">Administrator</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100"
          [value]="ManagedAccountModel.administrator" />         
        </div>  
        <div class="col-md-4 padding-card-rem" id="custodian">                      
          <label class="mb-0" for="custodian">Custodian</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.custodian"/>         
        </div>  
        <div class="col-md-4 padding-card-rem" id="legal-counsel">                      
          <label class="mb-0" for="legalCounsel">Legal Counsel</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.legalCounsel"/>         
        </div>  
        <div class="col-md-4 padding-card-rem" id="lei">                      
          <label class="mb-0" for="lei">LEI</label>
          <input type="text" 
          class="form-control eachlabel-padding default-txt TextTruncate input-text no-pointer"
          name="input-text" 
          autocomplete="off" 
          maxlength="100" 
          [value]="ManagedAccountModel.lei"/>         
        </div>  
      </div>
    </div>

    <div class="investment-summary-container">

      <div class="d-flex align-items-center mb-2">
        <div class="header-field" id="investment-summary-heading">
          Investment Summary           
        </div>
        <div class="edit-Button" (click)="triggerGoToStep(2)">
          <img src="assets/dist/images/k-edit.svg" alt="Edit" class="ml-4">
        </div>
      </div>
      
      <div class="row mr-0 ml-0 card-body padding-card-rem" id="investment-summary">
        <label for="investmentSummary">Summary</label>        
        <div id="investment-summary" class="investment-summary-content pb-2">
          {{ManagedAccountModel.investmentSummary}} 
        </div>
      </div>
    </div>
   
  </div>
