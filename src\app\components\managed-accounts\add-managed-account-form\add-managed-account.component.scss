@import "../../../../variables.scss";

.step {
  cursor: pointer;
}

button {
  background: var(--Color-Primary-Primary---78, #4061C7);
  color: white;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.step-sidebar {
  border-right: 1px solid #E6E6E6;
}

.btn-save {
  height: 32px !important;
  padding: 6px 16px !important;
}

.btn-save:disabled {
  opacity: 0.5;
}

.heading {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: left;
  padding: 24px;
}

.form-container {
  border: 1px solid #E6E6E6; 
  border-radius: 8px; 
  height: 580px;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  background-color: #fff;
  position: absolute;
  bottom: -37px;
  width: -webkit-fill-available;
}

button[kendoButton] {
  margin-right: 10px;
}

.btn-reset-container {
  display: flex;
  justify-content: flex-start;
}

.btn-warning {
  background: #FFFFFF 0% 0% no-repeat padding-box !important;
  border: 1px solid #4061C7;
  border-radius: 4px;
  opacity: 1;
  color: var(--Color-Primary-Primary---78, #4061C7);
}

.custom-Fixedheight {
  height: 540px;
  padding-top: 2.5rem;
}

.active-step {
  color: #4061C7;
}

.step-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.step-title {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
}

.text-content {
  display: flex;
  flex-direction: column;
}

.step-image {
  width: 36px;
  height: 36px;
  margin: 5px;
}

.step-subtitle {
  font-size: 16px;
  font-weight: 500;
}

.stepper-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 40px;
  padding-left: 5px;
}

.stepper {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.stepper-line {
  border: 1px solid var(--Border-border-disabled-accent, #93B0ED);
  flex-grow: 1;
}

.imageIcon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  text-align: center;
}

.char-count {
  text-align: right;
  font-size: 0.875rem;
  color: #6c757d;
}

.custom-marginbottom {
  margin-bottom: 4px;
}

.disabled-save-btn {
  background: var(--Color-Primary-Primary---60, #93B0ED) !important;
  color: white !important;
}

.disabled-btn {
  border-radius: 4px;
  color: #93B0ED !important;
  border-color: #93B0ED !important;
  background-color: transparent !important;
}

.label-error {
  color: #DE3139;
}

.main-Container-height {
  height: 100%;
}
 .review-page-form-container {
  border: $review-page-form-container-border solid #E6E6E6;
  border-radius: $review-page-form-container-border-radius;
  height: 100%;
  margin: 0px 1rem 1rem 1rem;
}

.review-page-custom-Fixedheight {
  max-height: $review-page-max-height;
  padding: 5px;
  overflow-y: auto;
}

.review-page-custom-footer {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  width: -webkit-fill-available;
}


.popup-text {
  font-weight: 400;
  line-height: $popup-text-line-height;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

::-webkit-scrollbar {
  width: $review-page-scroll-width !important;
  color: $review-page-scroll-color !important;
}
